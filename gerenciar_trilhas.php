<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';

// Verificar permissão (apenas gestores e admins podem gerenciar)
if (!$edu_permissions->canManageTrilhas()) {
    header('Location: ../../access_denied_edu.php?projeto=educacao-corporativa&nivel=gestor');
    exit;
}

$message = '';
$message_type = '';

// Função para normalizar texto (remover acentos)
function normalizeTextPHP($text) {
    $text = strtolower($text);

    // Mapa de caracteres acentuados para não acentuados
    $acentos = [
        'á' => 'a', 'à' => 'a', 'ã' => 'a', 'â' => 'a', 'ä' => 'a',
        'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
        'í' => 'i', 'ì' => 'i', 'î' => 'i', 'ï' => 'i',
        'ó' => 'o', 'ò' => 'o', 'õ' => 'o', 'ô' => 'o', 'ö' => 'o',
        'ú' => 'u', 'ù' => 'u', 'û' => 'u', 'ü' => 'u',
        'ç' => 'c', 'ñ' => 'n',
        'Á' => 'a', 'À' => 'a', 'Ã' => 'a', 'Â' => 'a', 'Ä' => 'a',
        'É' => 'e', 'È' => 'e', 'Ê' => 'e', 'Ë' => 'e',
        'Í' => 'i', 'Ì' => 'i', 'Î' => 'i', 'Ï' => 'i',
        'Ó' => 'o', 'Ò' => 'o', 'Õ' => 'o', 'Ô' => 'o', 'Ö' => 'o',
        'Ú' => 'u', 'Ù' => 'u', 'Û' => 'u', 'Ü' => 'u',
        'Ç' => 'c', 'Ñ' => 'n'
    ];

    return strtr($text, $acentos);
}

// Função para redirecionar preservando filtros
function redirectWithFilters() {
    $params = [];

    if (!empty($_POST['filter_trilha'])) {
        $params['trilha'] = $_POST['filter_trilha'];
    }
    if (!empty($_POST['filter_curso'])) {
        $params['curso'] = $_POST['filter_curso'];
    }
    if (!empty($_POST['filter_status'])) {
        $params['status'] = $_POST['filter_status'];
    }

    $url = 'gerenciar_trilhas.php';
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    header('Location: ' . $url);
    exit;
}

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'toggle_obrigatoria':
                    try {
                        $codigo_trilha = $_POST['codigo_trilha'];
                        $trilha = $_POST['trilha'];
                        $obrigatoria = isset($_POST['obrigatoria']) ? 1 : 0;

                        // Verificar se já existe
                        $stmt = $pdo_edu->prepare("SELECT id, obrigatoria FROM edu_trilhas_obrigatorias WHERE codigo_trilha = ?");
                        $stmt->execute([$codigo_trilha]);
                        $existing = $stmt->fetch();

                    if ($existing) {
                        // Atualizar
                        $stmt = $pdo_edu->prepare("UPDATE edu_trilhas_obrigatorias SET obrigatoria = ?, usuario_atualizacao = ? WHERE id = ?");
                        $stmt->execute([$obrigatoria, $_SESSION['user_id'], $existing['id']]);

                        // Log
                        $stmt = $pdo_edu->prepare("INSERT INTO edu_log_trilhas_obrigatorias (trilha_id, acao, valores_anteriores, valores_novos, usuario_id) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute([
                            $existing['id'],
                            $obrigatoria ? 'marcada_obrigatoria' : 'desmarcada_obrigatoria',
                            json_encode(['obrigatoria' => $existing['obrigatoria']]),
                            json_encode(['obrigatoria' => $obrigatoria]),
                            $_SESSION['user_id']
                        ]);

                    } else {
                        // Criar novo
                        $stmt = $pdo_edu->prepare("INSERT INTO edu_trilhas_obrigatorias (codigo_trilha, trilha, obrigatoria, usuario_criacao) VALUES (?, ?, ?, ?)");
                        $stmt->execute([$codigo_trilha, $trilha, $obrigatoria, $_SESSION['user_id']]);

                        $trilha_id = $pdo_edu->lastInsertId();

                        // Log
                        $stmt = $pdo_edu->prepare("INSERT INTO edu_log_trilhas_obrigatorias (trilha_id, acao, valores_novos, usuario_id) VALUES (?, ?, ?, ?)");
                        $stmt->execute([
                            $trilha_id,
                            'criada',
                            json_encode(['obrigatoria' => $obrigatoria, 'codigo_trilha' => $codigo_trilha]),
                            $_SESSION['user_id']
                        ]);
                    }

                        $message = 'Configuração de trilha obrigatória atualizada com sucesso!';
                        $message_type = 'success';

                        // Redirecionar preservando filtros
                        redirectWithFilters();
                    } catch (PDOException $e) {
                        $message = 'Erro: Tabela de trilhas obrigatórias não encontrada. Execute o script SQL primeiro.';
                        $message_type = 'danger';
                    }
                    break;

                case 'toggle_prazo':
                    $codigo_trilha = $_POST['codigo_trilha'];
                    $codigo_recurso = $_POST['codigo_recurso'];
                    $trilha = $_POST['trilha'];
                    $recurso = $_POST['recurso'];
                    $ativo = isset($_POST['ativo']) ? 1 : 0;
                    
                    // Verificar se já existe
                    $stmt = $pdo_edu->prepare("SELECT id, prazo_personalizado_ativo FROM edu_prazos_personalizados WHERE codigo_trilha = ? AND codigo_recurso = ?");
                    $stmt->execute([$codigo_trilha, $codigo_recurso]);
                    $existing = $stmt->fetch();
                    
                    if ($existing) {
                        // Atualizar
                        $stmt = $pdo_edu->prepare("UPDATE edu_prazos_personalizados SET prazo_personalizado_ativo = ?, usuario_atualizacao = ? WHERE id = ?");
                        $stmt->execute([$ativo, $_SESSION['user_id'], $existing['id']]);
                        
                        // Log
                        $stmt = $pdo_edu->prepare("INSERT INTO edu_log_prazos (prazo_id, acao, valores_anteriores, valores_novos, usuario_id) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute([
                            $existing['id'],
                            $ativo ? 'ativado' : 'desativado',
                            json_encode(['prazo_personalizado_ativo' => $existing['prazo_personalizado_ativo']]),
                            json_encode(['prazo_personalizado_ativo' => $ativo]),
                            $_SESSION['user_id']
                        ]);
                        
                    } else {
                        // Criar novo
                        $stmt = $pdo_edu->prepare("INSERT INTO edu_prazos_personalizados (codigo_trilha, trilha, codigo_recurso, recurso, prazo_personalizado_ativo, usuario_criacao) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$codigo_trilha, $trilha, $codigo_recurso, $recurso, $ativo, $_SESSION['user_id']]);
                        
                        $prazo_id = $pdo_edu->lastInsertId();
                        
                        // Log
                        $stmt = $pdo_edu->prepare("INSERT INTO edu_log_prazos (prazo_id, acao, valores_novos, usuario_id) VALUES (?, ?, ?, ?)");
                        $stmt->execute([
                            $prazo_id,
                            'criado',
                            json_encode(['prazo_personalizado_ativo' => $ativo, 'codigo_trilha' => $codigo_trilha, 'codigo_recurso' => $codigo_recurso]),
                            $_SESSION['user_id']
                        ]);
                    }
                    
                    $message = 'Configuração atualizada com sucesso!';
                    $message_type = 'success';

                    // Redirecionar preservando filtros
                    redirectWithFilters();
                    break;
                    
                case 'update_prazos':
                    $prazo_id = $_POST['prazo_id'];
                    $primeiro_prazo = !empty($_POST['primeiro_prazo']) ? intval($_POST['primeiro_prazo']) : null;
                    $renovacao_prazo = !empty($_POST['renovacao_prazo']) ? intval($_POST['renovacao_prazo']) : null;
                    
                    // Buscar valores anteriores
                    $stmt = $pdo_edu->prepare("SELECT primeiro_prazo_dias, renovacao_prazo_dias FROM edu_prazos_personalizados WHERE id = ?");
                    $stmt->execute([$prazo_id]);
                    $old_values = $stmt->fetch();
                    
                    // Atualizar
                    $stmt = $pdo_edu->prepare("UPDATE edu_prazos_personalizados SET primeiro_prazo_dias = ?, renovacao_prazo_dias = ?, usuario_atualizacao = ? WHERE id = ?");
                    $stmt->execute([$primeiro_prazo, $renovacao_prazo, $_SESSION['user_id'], $prazo_id]);
                    
                    // Log
                    $stmt = $pdo_edu->prepare("INSERT INTO edu_log_prazos (prazo_id, acao, valores_anteriores, valores_novos, usuario_id) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $prazo_id,
                        'atualizado',
                        json_encode(['primeiro_prazo_dias' => $old_values['primeiro_prazo_dias'], 'renovacao_prazo_dias' => $old_values['renovacao_prazo_dias']]),
                        json_encode(['primeiro_prazo_dias' => $primeiro_prazo, 'renovacao_prazo_dias' => $renovacao_prazo]),
                        $_SESSION['user_id']
                    ]);
                    
                    $message = 'Prazos atualizados com sucesso!';
                    $message_type = 'success';

                    // Redirecionar preservando filtros
                    redirectWithFilters();
                    break;
            }
        }
    } catch (Exception $e) {
        $message = 'Erro: ' . $e->getMessage();
        $message_type = 'danger';
    }
}

// Buscar trilhas e cursos únicos do banco com carga horária
$stmt = $pdo_edu->query("
    SELECT DISTINCT
        codigo_trilha,
        trilha,
        codigo_recurso,
        recurso,
        carga_horaria_recurso
    FROM edu_relatorio_educacao
    WHERE codigo_trilha IS NOT NULL
    AND codigo_recurso IS NOT NULL
    ORDER BY trilha, recurso
");
$cursos_db = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar configurações de prazos existentes
$stmt = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados");
$prazos_config = [];
foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $config) {
    $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
    $prazos_config[$key] = $config;
}

// Buscar configurações de trilhas obrigatórias (com verificação se a tabela existe)
$trilhas_obrigatorias = [];
try {
    $stmt = $pdo_edu->query("SELECT * FROM edu_trilhas_obrigatorias");
    foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $config) {
        $trilhas_obrigatorias[$config['codigo_trilha']] = $config;
    }
} catch (PDOException $e) {
    // Tabela ainda não existe, continuar sem configurações de trilhas obrigatórias
    $trilhas_obrigatorias = [];
}

// CORREÇÃO: Para manter consistência com analise_colaboradores.php,
// obtemos a contagem de recursos únicos globalmente (COUNT DISTINCT recurso)
// Isso garante que ambas as páginas mostrem o mesmo número de cursos (400)
$stmt_recursos_unicos = $pdo_edu->query("SELECT COUNT(DISTINCT recurso) as total_recursos_unicos FROM edu_relatorio_educacao");
$total_recursos_unicos_global = $stmt_recursos_unicos->fetch()['total_recursos_unicos'];

// Agrupar cursos por trilha
$trilhas = [];
$recursos_unicos_global = []; // Array para controlar recursos únicos globalmente

foreach ($cursos_db as $curso) {
    $trilha_nome = $curso['trilha'];
    $recurso_nome = $curso['recurso'];

    if (!isset($trilhas[$trilha_nome])) {
        $config_obrigatoria = isset($trilhas_obrigatorias[$curso['codigo_trilha']]) ? $trilhas_obrigatorias[$curso['codigo_trilha']] : null;
        $trilhas[$trilha_nome] = [
            'codigo_trilha' => $curso['codigo_trilha'],
            'nome' => $trilha_nome,
            'obrigatoria' => $config_obrigatoria ? $config_obrigatoria['obrigatoria'] : false,
            'config_obrigatoria' => $config_obrigatoria,
            'cursos' => []
        ];
    }

    // Adicionar o recurso à lista global de recursos únicos
    if (!isset($recursos_unicos_global[$recurso_nome])) {
        $recursos_unicos_global[$recurso_nome] = true;
    }

    $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
    $config = isset($prazos_config[$key]) ? $prazos_config[$key] : null;

    $trilhas[$trilha_nome]['cursos'][] = [
        'codigo_recurso' => $curso['codigo_recurso'],
        'nome' => $curso['recurso'],
        'carga_horaria' => $curso['carga_horaria_recurso'],
        'config' => $config
    ];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Trilhas e Cursos - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
        }



        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }

        .trilha-card {
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .trilha-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
            padding: 1rem 1.5rem;
        }

        .curso-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background-color: #ffffff;
            transition: all 0.3s ease;
        }

        .curso-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .curso-ativo {
            border-color: var(--sicoob-verde-claro);
            background-color: #f8fff4;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--sicoob-turquesa);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .prazo-config {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border: 1px solid #dee2e6;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-success {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .stats-badge {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            color: var(--sicoob-verde-escuro);
            font-weight: bold;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
        }

        .filter-section {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-title {
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .trilha-toggle {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trilha-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .trilha-collapsed .card-body {
            display: none;
        }

        .collapse-icon {
            transition: transform 0.3s ease;
        }

        .trilha-collapsed .collapse-icon {
            transform: rotate(-90deg);
        }

        .filter-badge {
            background-color: var(--sicoob-turquesa);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            margin-left: 0.5rem;
        }

        .clear-filters {
            color: var(--sicoob-verde-escuro);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .clear-filters:hover {
            color: var(--sicoob-turquesa);
            text-decoration: underline;
        }

        .carga-horaria-badge {
            background: linear-gradient(135deg, var(--sicoob-turquesa), #00c4b4);
            color: var(--sicoob-branco);
            border: 1px solid var(--sicoob-turquesa);
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 174, 157, 0.2);
        }

        .carga-horaria-info {
            color: var(--sicoob-turquesa);
            font-weight: 600;
        }

        .curso-info-grid {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
        }

        .status-column {
            min-width: 120px;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-route me-2"></i>Gerenciar Trilhas e Cursos</h2>
                        <p class="text-muted">Configure prazos personalizados para cursos específicos</p>
                    </div>
                    <div>
                        <span class="stats-badge">
                            <i class="fas fa-layer-group me-1"></i>
                            <?php echo count($trilhas); ?> Trilhas
                        </span>
                        <span class="stats-badge ms-2">
                            <i class="fas fa-book me-1"></i>
                            <?php echo $total_recursos_unicos_global; ?> Cursos
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mensagens -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Seção de Filtros -->
        <div class="filter-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="filter-title mb-0">
                    <i class="fas fa-filter me-2"></i>Filtros
                </h6>
                <a href="#" class="clear-filters" onclick="clearAllFilters()">
                    <i class="fas fa-times me-1"></i>Limpar Filtros
                </a>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Trilha</label>
                    <select class="form-select" id="filterTrilha" onchange="applyFilters()">
                        <option value="">Todas as trilhas</option>
                        <?php foreach ($trilhas as $trilha): ?>
                        <option value="<?php echo htmlspecialchars($trilha['nome']); ?>">
                            <?php echo htmlspecialchars($trilha['nome']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Curso</label>
                    <input type="text" class="form-control" id="filterCurso"
                           placeholder="Digite ou selecione um curso..."
                           list="cursosList"
                           onkeyup="applyFilters()"
                           onchange="applyFilters()">
                    <datalist id="cursosList">
                        <?php
                        $cursos_unicos = [];
                        foreach ($trilhas as $trilha) {
                            foreach ($trilha['cursos'] as $curso) {
                                if (!in_array($curso['nome'], $cursos_unicos)) {
                                    $cursos_unicos[] = $curso['nome'];
                                }
                            }
                        }
                        sort($cursos_unicos);
                        foreach ($cursos_unicos as $curso_nome):
                        ?>
                        <option value="<?php echo htmlspecialchars($curso_nome); ?>">
                        <?php endforeach; ?>
                    </datalist>
                </div>

                <div class="col-md-2">
                    <label class="form-label">Status do Prazo</label>
                    <select class="form-select" id="filterStatus" onchange="applyFilters()">
                        <option value="">Todos os status</option>
                        <option value="ativo">Com Prazo Personalizado</option>
                        <option value="padrao">Prazo Padrão</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">Trilhas Obrigatórias</label>
                    <select class="form-select" id="filterObrigatoria" onchange="applyFilters()">
                        <option value="">Todas</option>
                        <option value="sim">Apenas Obrigatórias</option>
                        <option value="nao">Apenas Não Obrigatórias</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">Ações</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="expandAll()">
                            <i class="fas fa-expand-alt me-1"></i>Expandir
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="collapseAll()">
                            <i class="fas fa-compress-alt me-1"></i>Retrair
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filtros Ativos -->
            <div id="activeFilters" class="mt-3" style="display: none;">
                <small class="text-muted">Filtros ativos:</small>
                <div id="filterBadges" class="d-inline"></div>
            </div>
        </div>

        <!-- Informações -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h6><i class="fas fa-info-circle me-2"></i>Como Funciona</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>Prazo Padrão:</strong> Usa a data do relatório (coluna "Concluir trilha até")</li>
                                    <li><strong>Primeiro Prazo:</strong> Para colaboradores que nunca fizeram o curso (baseado na data de admissão)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>Renovação:</strong> Para colaboradores que já concluíram o curso (baseado na última conclusão)</li>
                                    <li><strong>Prazos em dias:</strong> Exemplo: 90 dias, 180 dias, 360 dias</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Trilhas e Cursos -->
        <?php foreach ($trilhas as $trilha_nome => $trilha): ?>
        <div class="card trilha-card"
             data-trilha="<?php echo htmlspecialchars($trilha['nome']); ?>"
             data-obrigatoria="<?php echo $trilha['obrigatoria'] ? 'sim' : 'nao'; ?>">
            <div class="trilha-header trilha-toggle" onclick="toggleTrilha(this)">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-chevron-down collapse-icon me-2"></i>
                            <i class="fas fa-route me-2"></i>
                            <?php echo htmlspecialchars($trilha['nome']); ?>
                            <span class="badge bg-light text-dark ms-2">
                                <?php echo count($trilha['cursos']); ?> curso(s)
                            </span>
                            <?php if ($trilha['obrigatoria']): ?>
                            <span class="badge bg-warning text-dark ms-1">
                                <i class="fas fa-exclamation-triangle me-1"></i>OBRIGATÓRIA
                            </span>
                            <?php endif; ?>
                            <?php
                            $cursosAtivos = array_filter($trilha['cursos'], function($c) {
                                return $c['config'] && $c['config']['prazo_personalizado_ativo'];
                            });
                            if (count($cursosAtivos) > 0):
                            ?>
                            <span class="badge bg-success ms-1">
                                <?php echo count($cursosAtivos); ?> personalizado(s)
                            </span>
                            <?php endif; ?>
                        </h5>
                        <small>Código: <?php echo htmlspecialchars($trilha['codigo_trilha']); ?></small>
                    </div>
                    <div class="text-end d-flex align-items-center">
                        <div class="me-3">
                            <form method="POST" class="d-inline" onsubmit="return preserveFiltersAndSubmit(this)" onclick="event.stopPropagation();">
                                <input type="hidden" name="action" value="toggle_obrigatoria">
                                <input type="hidden" name="codigo_trilha" value="<?php echo htmlspecialchars($trilha['codigo_trilha']); ?>">
                                <input type="hidden" name="trilha" value="<?php echo htmlspecialchars($trilha['nome']); ?>">
                                <!-- Campos para preservar filtros -->
                                <input type="hidden" name="filter_trilha" value="<?php echo isset($_GET['trilha']) ? htmlspecialchars($_GET['trilha']) : ''; ?>">
                                <input type="hidden" name="filter_curso" value="<?php echo isset($_GET['curso']) ? htmlspecialchars($_GET['curso']) : ''; ?>">
                                <input type="hidden" name="filter_status" value="<?php echo isset($_GET['status']) ? htmlspecialchars($_GET['status']) : ''; ?>">

                                <div class="d-flex align-items-center">
                                    <span class="me-2 small text-light">Trilha Obrigatória:</span>
                                    <label class="switch">
                                        <input type="checkbox" name="obrigatoria"
                                               <?php echo $trilha['obrigatoria'] ? 'checked' : ''; ?>
                                               onchange="preserveFiltersAndSubmit(this.form); this.form.submit();">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </form>
                        </div>
                        <div>
                            <small class="text-light">Clique para expandir/retrair</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php foreach ($trilha['cursos'] as $curso): ?>
                <div class="curso-item <?php echo ($curso['config'] && $curso['config']['prazo_personalizado_ativo']) ? 'curso-ativo' : ''; ?>"
                     data-curso="<?php echo htmlspecialchars($curso['nome']); ?>"
                     data-curso-normalized="<?php echo htmlspecialchars(normalizeTextPHP($curso['nome'])); ?>"
                     data-status="<?php echo ($curso['config'] && $curso['config']['prazo_personalizado_ativo']) ? 'ativo' : 'padrao'; ?>">
                    <div class="row align-items-center">
                        <div class="col-md-5">
                            <h6 class="mb-1">
                                <i class="fas fa-book me-2"></i>
                                <?php echo htmlspecialchars($curso['nome']); ?>
                            </h6>
                            <div class="curso-info-grid">
                                <div class="info-item text-muted">
                                    <i class="fas fa-code"></i>
                                    <span><?php echo htmlspecialchars($curso['codigo_recurso']); ?></span>
                                </div>

                            </div>
                        </div>
                        <div class="col-md-2">
                            <?php if (!empty($curso['carga_horaria'])): ?>
                            <div class="text-center">
                                <div class="badge carga-horaria-badge">
                                    <i class="fas fa-hourglass-half me-1"></i>
                                    <?php echo htmlspecialchars($curso['carga_horaria']); ?>
                                </div>
                                <div class="small text-muted">Carga Horária</div>
                            </div>
                            <?php else: ?>
                            <div class="text-center">
                                <div class="badge bg-secondary">
                                    <i class="fas fa-question me-1"></i>
                                    Não informado
                                </div>
                                <div class="small text-muted">Carga Horária</div>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <span class="me-2">Prazo Personalizado:</span>
                                <form method="POST" class="d-inline" onsubmit="return preserveFiltersAndSubmit(this)">
                                    <input type="hidden" name="action" value="toggle_prazo">
                                    <input type="hidden" name="codigo_trilha" value="<?php echo htmlspecialchars($trilha['codigo_trilha']); ?>">
                                    <input type="hidden" name="trilha" value="<?php echo htmlspecialchars($trilha['nome']); ?>">
                                    <input type="hidden" name="codigo_recurso" value="<?php echo htmlspecialchars($curso['codigo_recurso']); ?>">
                                    <input type="hidden" name="recurso" value="<?php echo htmlspecialchars($curso['nome']); ?>">
                                    <!-- Campos para preservar filtros -->
                                    <input type="hidden" name="filter_trilha" value="<?php echo isset($_GET['trilha']) ? htmlspecialchars($_GET['trilha']) : ''; ?>">
                                    <input type="hidden" name="filter_curso" value="<?php echo isset($_GET['curso']) ? htmlspecialchars($_GET['curso']) : ''; ?>">
                                    <input type="hidden" name="filter_status" value="<?php echo isset($_GET['status']) ? htmlspecialchars($_GET['status']) : ''; ?>">

                                    <label class="switch">
                                        <input type="checkbox" name="ativo"
                                               <?php echo ($curso['config'] && $curso['config']['prazo_personalizado_ativo']) ? 'checked' : ''; ?>
                                               onchange="preserveFiltersAndSubmit(this.form); this.form.submit();">
                                        <span class="slider"></span>
                                    </label>
                                </form>
                            </div>
                        </div>
                        <div class="col-md-2 status-column">
                            <div class="text-center">
                                <?php if ($curso['config'] && $curso['config']['prazo_personalizado_ativo']): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>Ativo
                                </span>
                                <?php else: ?>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-clock me-1"></i>Padrão
                                </span>
                                <?php endif; ?>
                                <div class="small text-muted">Status</div>
                            </div>
                        </div>
                    </div>

                    <!-- Configuração de Prazos -->
                    <?php if ($curso['config'] && $curso['config']['prazo_personalizado_ativo']): ?>
                    <div class="prazo-config">
                        <form method="POST" onsubmit="return preserveFiltersAndSubmit(this)">
                            <input type="hidden" name="action" value="update_prazos">
                            <input type="hidden" name="prazo_id" value="<?php echo $curso['config']['id']; ?>">
                            <!-- Campos para preservar filtros -->
                            <input type="hidden" name="filter_trilha" value="<?php echo isset($_GET['trilha']) ? htmlspecialchars($_GET['trilha']) : ''; ?>">
                            <input type="hidden" name="filter_curso" value="<?php echo isset($_GET['curso']) ? htmlspecialchars($_GET['curso']) : ''; ?>">
                            <input type="hidden" name="filter_status" value="<?php echo isset($_GET['status']) ? htmlspecialchars($_GET['status']) : ''; ?>">

                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">
                                        <i class="fas fa-user-plus me-1"></i>Primeiro Prazo (dias)
                                    </label>
                                    <input type="number" class="form-control" name="primeiro_prazo"
                                           value="<?php echo $curso['config']['primeiro_prazo_dias']; ?>"
                                           placeholder="Ex: 90" min="1" max="3650">
                                    <small class="text-muted">Para quem nunca fez o curso</small>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">
                                        <i class="fas fa-redo me-1"></i>Renovação (dias)
                                    </label>
                                    <input type="number" class="form-control" name="renovacao_prazo"
                                           value="<?php echo $curso['config']['renovacao_prazo_dias']; ?>"
                                           placeholder="Ex: 180" min="1" max="3650">
                                    <small class="text-muted">Para quem já concluiu</small>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>Salvar Prazos
                                    </button>
                                </div>
                            </div>
                        </form>

                        <?php if ($curso['config']['primeiro_prazo_dias'] || $curso['config']['renovacao_prazo_dias']): ?>
                        <div class="mt-2 pt-2 border-top">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Configurado em: <?php echo date('d/m/Y H:i', strtotime($curso['config']['data_atualizacao'] ?: $curso['config']['data_criacao'])); ?>
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <?php if (empty($trilhas)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5>Nenhuma trilha encontrada</h5>
                <p class="text-muted">Importe um relatório primeiro para visualizar as trilhas e cursos disponíveis.</p>
                <a href="importar.php" class="btn btn-primary">
                    <i class="fas fa-file-upload me-2"></i>Importar Relatório
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para normalizar texto (remover acentos e converter para minúsculo)
        function normalizeText(text) {
            return text
                .toLowerCase()
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '') // Remove diacríticos (acentos)
                .trim();
        }

        // Função para alternar trilha (expandir/retrair)
        function toggleTrilha(header) {
            const card = header.closest('.trilha-card');
            card.classList.toggle('trilha-collapsed');
        }

        // Expandir todas as trilhas
        function expandAll() {
            document.querySelectorAll('.trilha-card').forEach(card => {
                card.classList.remove('trilha-collapsed');
            });
        }

        // Retrair todas as trilhas
        function collapseAll() {
            document.querySelectorAll('.trilha-card').forEach(card => {
                card.classList.add('trilha-collapsed');
            });
        }

        // Aplicar filtros
        function applyFilters() {
            const filterTrilha = normalizeText(document.getElementById('filterTrilha').value);
            const filterCurso = normalizeText(document.getElementById('filterCurso').value);
            const filterStatus = document.getElementById('filterStatus').value;
            const filterObrigatoria = document.getElementById('filterObrigatoria').value;

            let visibleTrilhas = 0;
            let visibleCursos = 0;

            // Filtrar trilhas
            document.querySelectorAll('.trilha-card').forEach(trilhaCard => {
                const trilhaNome = normalizeText(trilhaCard.dataset.trilha);
                const trilhaObrigatoria = trilhaCard.dataset.obrigatoria;
                let trilhaVisible = false;

                // Verificar se a trilha passa no filtro
                const trilhaMatch = !filterTrilha || trilhaNome.includes(filterTrilha);
                const obrigatoriaMatch = !filterObrigatoria || trilhaObrigatoria === filterObrigatoria;

                if (trilhaMatch && obrigatoriaMatch) {
                    // Filtrar cursos dentro da trilha
                    const cursos = trilhaCard.querySelectorAll('.curso-item');
                    let cursosVisiveis = 0;

                    cursos.forEach(curso => {
                        // Usar atributo normalizado se disponível, senão normalizar em tempo real
                        const cursoNome = curso.dataset.cursoNormalized || normalizeText(curso.dataset.curso);
                        const cursoStatus = curso.dataset.status;

                        const cursoMatch = !filterCurso || cursoNome.includes(filterCurso);
                        const statusMatch = !filterStatus || cursoStatus === filterStatus;

                        if (cursoMatch && statusMatch) {
                            curso.style.display = 'block';
                            cursosVisiveis++;
                            visibleCursos++;
                        } else {
                            curso.style.display = 'none';
                        }
                    });

                    // Mostrar trilha se tem cursos visíveis
                    if (cursosVisiveis > 0) {
                        trilhaCard.style.display = 'block';
                        trilhaVisible = true;
                        visibleTrilhas++;

                        // Expandir trilha se há filtros ativos
                        if (filterCurso || filterStatus) {
                            trilhaCard.classList.remove('trilha-collapsed');
                        }
                    } else {
                        trilhaCard.style.display = 'none';
                    }
                } else {
                    trilhaCard.style.display = 'none';
                }
            });

            // Atualizar badges de filtros ativos
            updateFilterBadges();

            // Mostrar mensagem se nenhum resultado
            updateNoResultsMessage(visibleTrilhas, visibleCursos);

            // Atualizar URL com filtros atuais
            updateURL();

            // Atualizar filtros em todos os formulários
            updateAllFormFilters();
        }

        // Atualizar badges de filtros ativos
        function updateFilterBadges() {
            const filterTrilha = document.getElementById('filterTrilha').value;
            const filterCurso = document.getElementById('filterCurso').value;
            const filterStatus = document.getElementById('filterStatus').value;
            const filterObrigatoria = document.getElementById('filterObrigatoria').value;

            const activeFilters = document.getElementById('activeFilters');
            const filterBadges = document.getElementById('filterBadges');

            let badges = [];

            if (filterTrilha) {
                badges.push(`<span class="filter-badge">Trilha: ${filterTrilha}</span>`);
            }
            if (filterCurso) {
                badges.push(`<span class="filter-badge">Curso: ${filterCurso}</span>`);
            }
            if (filterStatus) {
                const statusText = filterStatus === 'ativo' ? 'Com Prazo Personalizado' : 'Prazo Padrão';
                badges.push(`<span class="filter-badge">Status: ${statusText}</span>`);
            }
            if (filterObrigatoria) {
                const obrigatoriaText = filterObrigatoria === 'sim' ? 'Apenas Obrigatórias' : 'Apenas Não Obrigatórias';
                badges.push(`<span class="filter-badge">Trilhas: ${obrigatoriaText}</span>`);
            }

            if (badges.length > 0) {
                filterBadges.innerHTML = badges.join(' ');
                activeFilters.style.display = 'block';
            } else {
                activeFilters.style.display = 'none';
            }
        }

        // Atualizar mensagem de "nenhum resultado"
        function updateNoResultsMessage(visibleTrilhas, visibleCursos) {
            let existingMessage = document.getElementById('noResultsMessage');

            if (visibleTrilhas === 0) {
                if (!existingMessage) {
                    const message = document.createElement('div');
                    message.id = 'noResultsMessage';
                    message.className = 'card';
                    message.innerHTML = `
                        <div class="card-body text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5>Nenhum resultado encontrado</h5>
                            <p class="text-muted">Tente ajustar os filtros para encontrar o que procura.</p>
                            <button type="button" class="btn btn-outline-primary" onclick="clearAllFilters()">
                                <i class="fas fa-times me-2"></i>Limpar Filtros
                            </button>
                        </div>
                    `;

                    // Inserir após a seção de informações
                    const infoSection = document.querySelector('.row.mb-4');
                    infoSection.parentNode.insertBefore(message, infoSection.nextSibling);
                }
            } else {
                if (existingMessage) {
                    existingMessage.remove();
                }
            }
        }

        // Limpar todos os filtros
        function clearAllFilters() {
            document.getElementById('filterTrilha').value = '';
            document.getElementById('filterCurso').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterObrigatoria').value = '';

            // Mostrar todas as trilhas e cursos
            document.querySelectorAll('.trilha-card').forEach(card => {
                card.style.display = 'block';
            });

            document.querySelectorAll('.curso-item').forEach(curso => {
                curso.style.display = 'block';
            });

            // Limpar badges
            document.getElementById('activeFilters').style.display = 'none';

            // Remover mensagem de "nenhum resultado"
            const noResultsMessage = document.getElementById('noResultsMessage');
            if (noResultsMessage) {
                noResultsMessage.remove();
            }

            // Limpar URL
            window.history.replaceState({}, '', window.location.pathname);
        }

        // Função para preservar filtros nos formulários
        function preserveFilters(form) {
            const filterTrilha = document.getElementById('filterTrilha').value;
            const filterCurso = document.getElementById('filterCurso').value;
            const filterStatus = document.getElementById('filterStatus').value;

            // Atualizar campos hidden do formulário
            const trilhaField = form.querySelector('input[name="filter_trilha"]');
            const cursoField = form.querySelector('input[name="filter_curso"]');
            const statusField = form.querySelector('input[name="filter_status"]');

            if (trilhaField) trilhaField.value = filterTrilha;
            if (cursoField) cursoField.value = filterCurso;
            if (statusField) statusField.value = filterStatus;
        }

        // Função para preservar filtros e submeter formulário
        function preserveFiltersAndSubmit(form) {
            // Preservar filtros atuais
            preserveFilters(form);

            // Permitir que o formulário seja submetido
            return true;
        }

        // Atualizar URL com filtros atuais
        function updateURL() {
            const filterTrilha = document.getElementById('filterTrilha').value;
            const filterCurso = document.getElementById('filterCurso').value;
            const filterStatus = document.getElementById('filterStatus').value;

            const params = new URLSearchParams();

            if (filterTrilha) params.set('trilha', filterTrilha);
            if (filterCurso) params.set('curso', filterCurso);
            if (filterStatus) params.set('status', filterStatus);

            const newURL = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
            window.history.replaceState({}, '', newURL);
        }

        // Inicializar página
        document.addEventListener('DOMContentLoaded', function() {
            // Aplicar filtros iniciais se houver parâmetros na URL
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('trilha')) {
                document.getElementById('filterTrilha').value = urlParams.get('trilha');
            }
            if (urlParams.get('curso')) {
                document.getElementById('filterCurso').value = urlParams.get('curso');
            }
            if (urlParams.get('status')) {
                document.getElementById('filterStatus').value = urlParams.get('status');
            }

            // Aplicar filtros se houver parâmetros
            if (urlParams.toString()) {
                applyFilters();
            }

            // Garantir que todos os formulários tenham os filtros atuais
            updateAllFormFilters();
        });

        // Função para atualizar filtros em todos os formulários
        function updateAllFormFilters() {
            const filterTrilha = document.getElementById('filterTrilha').value;
            const filterCurso = document.getElementById('filterCurso').value;
            const filterStatus = document.getElementById('filterStatus').value;

            // Atualizar todos os formulários na página
            document.querySelectorAll('form').forEach(form => {
                const trilhaField = form.querySelector('input[name="filter_trilha"]');
                const cursoField = form.querySelector('input[name="filter_curso"]');
                const statusField = form.querySelector('input[name="filter_status"]');

                if (trilhaField) trilhaField.value = filterTrilha;
                if (cursoField) cursoField.value = filterCurso;
                if (statusField) statusField.value = filterStatus;
            });
        }
    </script>
</body>
</html>
