<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/EmailManager.php';
require_once 'classes/IntranetAPI.php';

// Teste para verificar consistência entre dados do card e preview do email

// CPF de teste (substitua por um CPF real do sistema)
$cpf_teste = '12345678901'; // Substitua por um CPF real

echo "<h2>Teste de Consistência de Dados</h2>";
echo "<p>Testando CPF: " . $cpf_teste . "</p>";

// 1. Simular cálculo do card (lógica do analise_colaboradores.php)
echo "<h3>1. Dados do Card (analise_colaboradores.php)</h3>";

// Buscar configurações de prazos personalizados
$stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
$prazos_config = [];
foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
    $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
    $prazos_config[$key] = $config;
}

// Buscar cursos do colaborador
$stmt_cursos = $pdo_edu->prepare("
    SELECT
        codigo_trilha,
        codigo_recurso,
        trilha,
        recurso,
        aprovacao,
        data_conclusao,
        concluir_trilha_ate,
        data_admissao,
        andamento_etapa
    FROM edu_relatorio_educacao
    WHERE cpf = ?
    ORDER BY trilha, recurso
");
$stmt_cursos->execute([$cpf_teste]);
$cursos_raw = $stmt_cursos->fetchAll(PDO::FETCH_ASSOC);

// Função para calcular status de prazo (copiada do analise_colaboradores.php)
function calcularStatusPrazoOtimizado($curso, $prazos_config) {
    $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];

    // Se tem prazo personalizado e colaborador é elegível
    if (isset($prazos_config[$key]) && $curso['data_admissao'] > '2023-01-01') {
        $config = $prazos_config[$key];
        $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
        $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

        if ($primeiro_prazo > 0) {
            // Lógica simplificada para prazos personalizados
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                if ($renovacao_prazo <= 0) {
                    return 'concluido_sem_renovacao'; // Sem renovação
                }
                // Calcular prazo de renovação
                $data_conclusao = new DateTime($curso['data_conclusao']);
                $prazo_renovacao = clone $data_conclusao;
                $prazo_renovacao->add(new DateInterval('P' . $renovacao_prazo . 'D'));
                $hoje = new DateTime();

                if ($prazo_renovacao < $hoje) {
                    return 'vencido';
                } elseif ($prazo_renovacao <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            } else {
                // Primeiro prazo
                $data_admissao = new DateTime($curso['data_admissao']);
                $primeiro_prazo_data = clone $data_admissao;
                $primeiro_prazo_data->add(new DateInterval('P' . $primeiro_prazo . 'D'));
                $hoje = new DateTime();

                if ($primeiro_prazo_data < $hoje) {
                    return 'vencido';
                } elseif ($primeiro_prazo_data <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            }
        }
    }

    // Usar status básico já calculado na query
    if (!empty($curso['concluir_trilha_ate'])) {
        $hoje = new DateTime();
        $prazo = new DateTime($curso['concluir_trilha_ate']);
        
        if ($curso['aprovacao'] === 'Sim') {
            return 'aprovado'; // Curso já aprovado
        }
        
        if ($prazo < $hoje) {
            return 'vencido';
        } elseif ($prazo <= (clone $hoje)->add(new DateInterval('P30D'))) {
            return 'a_vencer';
        }
        return 'em_dia';
    }
    
    return 'em_dia';
}

// Calcular métricas do card
$cursos_vencidos_card = 0;
$cursos_a_vencer_card = 0;
$cursos_detalhes_card = [];

foreach ($cursos_raw as $curso) {
    $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);
    
    if ($status_final === 'vencido') {
        $cursos_vencidos_card++;
        $cursos_detalhes_card[] = $curso['trilha'] . ' - ' . $curso['recurso'] . ' (VENCIDO)';
    } elseif ($status_final === 'a_vencer') {
        $cursos_a_vencer_card++;
        $cursos_detalhes_card[] = $curso['trilha'] . ' - ' . $curso['recurso'] . ' (A VENCER)';
    }
}

echo "<p><strong>Cursos Vencidos (Card):</strong> " . $cursos_vencidos_card . "</p>";
echo "<p><strong>Cursos A Vencer (Card):</strong> " . $cursos_a_vencer_card . "</p>";
echo "<p><strong>Detalhes dos Cursos:</strong></p>";
echo "<ul>";
foreach ($cursos_detalhes_card as $detalhe) {
    echo "<li>" . htmlspecialchars($detalhe) . "</li>";
}
echo "</ul>";

// 2. Simular cálculo do preview (lógica do ajax_template_preview.php)
echo "<h3>2. Dados do Preview (ajax_template_preview.php)</h3>";

// Usar EmailManager para buscar dados
$emailManager = new EmailManager($pdo_edu);

// Buscar dados básicos do colaborador
$stmt = $pdo_edu->prepare("
    SELECT
        cpf,
        usuario,
        email,
        funcao,
        codigo_unidade,
        data_admissao,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT recurso) as total_cursos,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados
    FROM edu_relatorio_educacao
    WHERE cpf = ?
    GROUP BY cpf, usuario, email, funcao, codigo_unidade, data_admissao
");
$stmt->execute([$cpf_teste]);
$colaborador = $stmt->fetch(PDO::FETCH_ASSOC);

if ($colaborador) {
    // Calcular métricas corretas baseadas nos prazos personalizados (nova lógica)
    $cursos_vencidos_preview = 0;
    $cursos_a_vencer_preview = 0;
    $cursos_detalhes_preview = [];

    foreach ($cursos_raw as $curso) {
        $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);
        
        if ($status_final === 'vencido') {
            $cursos_vencidos_preview++;
            $cursos_detalhes_preview[] = $curso['trilha'] . ' - ' . $curso['recurso'] . ' (VENCIDO)';
        } elseif ($status_final === 'a_vencer') {
            $cursos_a_vencer_preview++;
            $cursos_detalhes_preview[] = $curso['trilha'] . ' - ' . $curso['recurso'] . ' (A VENCER)';
        }
    }

    echo "<p><strong>Cursos Vencidos (Preview):</strong> " . $cursos_vencidos_preview . "</p>";
    echo "<p><strong>Cursos A Vencer (Preview):</strong> " . $cursos_a_vencer_preview . "</p>";
    echo "<p><strong>Detalhes dos Cursos:</strong></p>";
    echo "<ul>";
    foreach ($cursos_detalhes_preview as $detalhe) {
        echo "<li>" . htmlspecialchars($detalhe) . "</li>";
    }
    echo "</ul>";

    // 3. Comparação
    echo "<h3>3. Comparação</h3>";
    
    $vencidos_iguais = ($cursos_vencidos_card === $cursos_vencidos_preview);
    $a_vencer_iguais = ($cursos_a_vencer_card === $cursos_a_vencer_preview);
    
    echo "<p><strong>Cursos Vencidos:</strong> " . 
         ($vencidos_iguais ? "✅ CONSISTENTE" : "❌ INCONSISTENTE") . 
         " (Card: $cursos_vencidos_card, Preview: $cursos_vencidos_preview)</p>";
    
    echo "<p><strong>Cursos A Vencer:</strong> " . 
         ($a_vencer_iguais ? "✅ CONSISTENTE" : "❌ INCONSISTENTE") . 
         " (Card: $cursos_a_vencer_card, Preview: $cursos_a_vencer_preview)</p>";
    
    if ($vencidos_iguais && $a_vencer_iguais) {
        echo "<p style='color: green; font-weight: bold;'>🎉 PROBLEMA RESOLVIDO! Os dados estão consistentes.</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>⚠️ AINDA HÁ INCONSISTÊNCIA. Verificar lógica.</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Colaborador não encontrado. Verifique o CPF de teste.</p>";
}

echo "<hr>";
echo "<p><em>Para testar com um CPF real, edite o arquivo e substitua a variável \$cpf_teste.</em></p>";
?>
